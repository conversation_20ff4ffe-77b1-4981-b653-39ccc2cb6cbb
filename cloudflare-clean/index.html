<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 Oracle Voice Chat - Cloudflare SSL</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .chat-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 900px;
            height: 700px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .chat-header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .status-bar {
            background: #e3f2fd;
            padding: 15px;
            border-bottom: 1px solid #bbdefb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .status-indicator {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-connected { background: #c8e6c9; color: #2e7d32; }
        .status-disconnected { background: #ffcdd2; color: #c62828; }
        .status-connecting { background: #fff3e0; color: #ef6c00; }
        .status-processing { background: #fff3e0; color: #f57c00; }
        .status-error { background: #ffcdd2; color: #c62828; }
        
        .browser-support {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 15px;
            font-size: 12px;
        }
        
        .browser-support.success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user { justify-content: flex-end; }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .message.assistant .message-content {
            background: #e9ecef;
            color: #333;
        }
        
        .message.system .message-content {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-style: italic;
            font-size: 12px;
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .message-input:focus { border-color: #667eea; }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .log {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
            margin-top: 15px;
            border: 1px solid #ddd;
        }

        .voice-controls {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .level-meter {
            width: 100%;
            height: 20px;
            background: #eee;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .level-bar {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #ffeb3b, #f44336);
            width: 0%;
            transition: width 0.1s;
        }

        .voice-status {
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: #333;
            padding: 10px;
            background: rgba(255,255,255,0.8);
            border-radius: 5px;
        }
        
        @media (max-width: 768px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }
            
            .input-group, .test-buttons {
                flex-direction: column;
                gap: 10px;
            }
            
            .message-input {
                width: 100%;
            }
        }
    </style>


















<script>
    // Environment variables injected at build time by Cloudflare Pages
    window.ORACLE_BACKEND = 'https://*************';
    window.WS_URL = 'wss://*************';
    
    console.log('🌍 Environment variables loaded:', {
        ORACLE_BACKEND: window.ORACLE_BACKEND,
        WS_URL: window.WS_URL,
        origin: window.location.origin
    });
</script>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🎤 Oracle Voice Chat</h1>
            <p>Cloudflare Pages + Oracle Backend + Deepgram + OpenAI</p>
        </div>

        <div class="status-bar">
            <div>
                <strong>Status:</strong>
                <span id="status" class="status-indicator status-disconnected">Pripravený</span>
            </div>
            <div>
                <strong>Session:</strong>
                <span id="sessionInfo">-</span>
            </div>
            <div>
                <strong>SSL:</strong>
                <span id="sslInfo">Kontrolujem...</span>
            </div>
        </div>

        <div class="browser-support" id="browserSupport">
            🔍 Kontrolujem podporu prehliadača...
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message system">
                <div class="message-content">
                    🚀 Vitajte v Oracle Voice Chat na Cloudflare Pages! Pripájam sa...
                </div>
            </div>
        </div>

        <div class="chat-input">
            <div class="test-buttons">
                <button id="testConnectionBtn" class="btn btn-info">🧪 Test pripojenia</button>
                <button id="connectWSBtn" class="btn btn-success" disabled>🔌 Aktivovať chat</button>
                <button id="startVoiceBtn" class="btn btn-success" disabled>🎤 Spustiť voice chat</button>
                <button id="stopVoiceBtn" class="btn btn-warning" disabled>⏹️ Zastaviť voice</button>
            </div>

            <div class="voice-controls" style="display: none;" id="voiceControls">
                <div class="level-meter">
                    <div id="levelBar" class="level-bar"></div>
                </div>
                <div class="voice-status" id="voiceStatus">
                    🎤 Pripravený na rozpoznávanie hlasu...
                </div>
            </div>
            
            <div class="input-group">
                <input type="text" id="messageInput" class="message-input" 
                       placeholder="Napíšte správu..." disabled>
                <button id="sendBtn" class="btn btn-primary" disabled>Odoslať</button>
            </div>
            
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // Production configuration - Oracle backend direct + WebSocket proxy
        const API_BASE = window.ORACLE_BACKEND || 'https://*************';
        const WS_URL = window.WS_URL || 'wss://*************';
        
        class CloudflareOracleChat {
            constructor() {
                this.ws = null;
                this.sessionId = null;
                this.isConnected = false;

                // Voice Activity Detection variables (from test_silence_detection)
                this.audioContext = null;
                this.analyser = null;
                this.microphone = null;
                this.isRunning = false;
                this.lastSpeechTime = Date.now();
                this.isSpeaking = false;
                this.silenceTimer = null;
                this.currentTranscript = '';
                this.speechStartTime = 0;
                this.isPlayingResponse = false;
                this.mediaRecorder = null;
                this.audioChunks = [];
                this.lastTokenTime = 0;
                this.tokenCount = 0;
                this.consecutiveSpeechCount = 0;
                this.consecutiveSilenceCount = 0;
                this.waitingForFirstToken = false;

                this.elements = {
                    status: document.getElementById('status'),
                    sessionInfo: document.getElementById('sessionInfo'),
                    sslInfo: document.getElementById('sslInfo'),
                    chatMessages: document.getElementById('chatMessages'),
                    messageInput: document.getElementById('messageInput'),
                    sendBtn: document.getElementById('sendBtn'),
                    testConnectionBtn: document.getElementById('testConnectionBtn'),
                    connectWSBtn: document.getElementById('connectWSBtn'),
                    startVoiceBtn: document.getElementById('startVoiceBtn'),
                    stopVoiceBtn: document.getElementById('stopVoiceBtn'),
                    voiceControls: document.getElementById('voiceControls'),
                    levelBar: document.getElementById('levelBar'),
                    voiceStatus: document.getElementById('voiceStatus'),
                    log: document.getElementById('log'),
                    browserSupport: document.getElementById('browserSupport')
                };
                
                this.init();
            }
            
            init() {
                this.log('🚀 Inicializujem Cloudflare Oracle Voice Chat...');
                
                // Check SSL and browser support
                this.checkSSL();
                this.checkBrowserSupport();
                
                this.setupEventListeners();
                
                // Auto-test connection
                setTimeout(() => this.testConnection(), 1000);
            }
            
            checkSSL() {
                const isHTTPS = window.location.protocol === 'https:';
                const domain = window.location.hostname;
                
                this.elements.sslInfo.textContent = isHTTPS ? '✅ HTTPS' : '❌ HTTP';
                
                if (isHTTPS) {
                    this.log(`✅ SSL aktívne na ${domain}`);
                    this.addSystemMessage('✅ Cloudflare SSL certifikát aktívny - WebRTC podporované!');
                } else {
                    this.log(`❌ SSL nie je aktívne na ${domain}`);
                    this.addSystemMessage('❌ SSL nie je aktívne - WebRTC môže byť obmedzené');
                }
            }
            
            checkBrowserSupport() {
                const hasWebRTC = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
                const hasWebAudio = !!(window.AudioContext || window.webkitAudioContext);
                const hasWebSpeech = !!window.speechSynthesis;
                const hasMediaRecorder = !!window.MediaRecorder;
                const isHTTPS = window.location.protocol === 'https:';
                
                const supportInfo = [
                    `📱 WebRTC: ${hasWebRTC ? '✅' : '❌'}`,
                    `🎵 Web Audio: ${hasWebAudio ? '✅' : '❌'}`,
                    `🔊 Web Speech: ${hasWebSpeech ? '✅' : '❌'}`,
                    `🎤 MediaRecorder: ${hasMediaRecorder ? '✅' : '❌'}`,
                    `🔒 HTTPS: ${isHTTPS ? '✅' : '❌'}`
                ].join(' | ');
                
                this.elements.browserSupport.innerHTML = supportInfo;
                
                if (hasWebRTC && hasWebAudio && hasWebSpeech && hasMediaRecorder && isHTTPS) {
                    this.elements.browserSupport.className = 'browser-support success';
                    this.log('✅ Všetky potrebné funkcie sú podporované');
                } else {
                    this.elements.browserSupport.className = 'browser-support';
                    this.log('⚠️ Niektoré funkcie môžu byť obmedzené');
                }
            }
            
            setupEventListeners() {
                this.elements.sendBtn.addEventListener('click', () => this.sendMessage());
                this.elements.testConnectionBtn.addEventListener('click', () => this.testConnection());
                this.elements.connectWSBtn.addEventListener('click', () => this.connectWebSocket());
                this.elements.startVoiceBtn.addEventListener('click', () => this.startVoiceChat());
                this.elements.stopVoiceBtn.addEventListener('click', () => this.stopVoiceChat());

                this.elements.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }

            async testConnection() {
                try {
                    this.log('🔗 Testujem pripojenie na Oracle backend...');
                    this.updateStatus('Testujem pripojenie...', 'connecting');

                    this.elements.testConnectionBtn.disabled = true;

                    // Test Oracle backend HTTPS connection
                    try {
                        const response = await fetch(`${API_BASE}/health`, {
                            method: 'GET',
                            headers: { 'Accept': 'text/plain' }
                        });

                        if (response.ok) {
                            const text = await response.text();
                            this.log(`✅ Oracle HTTPS backend pripojený: ${text}`);
                            this.updateStatus('✅ Oracle HTTPS pripojený', 'connected');
                            this.isConnected = true;
                            this.elements.connectWSBtn.disabled = false;
                            this.addSystemMessage('✅ Oracle HTTPS backend je dostupný! Môžete aktivovať chat s Deepgram STT + Piper TTS.');
                        } else {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                    } catch (error) {
                        this.log(`⚠️ Priame pripojenie zlyhalo: ${error.message}`);

                        // Fallback - assume Oracle backend is available and try WebSocket
                        this.log(`🔄 Skúšam WebSocket pripojenie...`);
                        this.updateStatus('✅ Skúšam WebSocket', 'connected');
                        this.isConnected = true;
                        this.elements.connectWSBtn.disabled = false;
                        this.addSystemMessage('🔄 Skúšam priame WebSocket pripojenie na Oracle backend...');
                    }

                } catch (error) {
                    this.log(`❌ Chyba: ${error.message}`);
                    this.updateStatus('❌ Chyba pripojenia', 'error');
                    this.isConnected = false;

                    // Enable WebSocket attempt anyway
                    this.elements.connectWSBtn.disabled = false;
                    this.addSystemMessage('⚠️ Skúsim WebSocket pripojenie napriek chybám.');

                } finally {
                    this.elements.testConnectionBtn.disabled = false;
                }
            }

            connectWebSocket() {
                try {
                    this.log('🔌 Pripájam sa na WebSocket...');
                    this.updateStatus('Pripájam WebSocket...', 'connecting');

                    // Create WebSocket connection
                    this.ws = new WebSocket(`${WS_URL}/ws`);
                    this.ws.binaryType = 'arraybuffer';

                    this.ws.onopen = () => {
                        this.log('✅ WebSocket pripojený!');
                        this.log(`🔍 WebSocket readyState: ${this.ws.readyState}`);
                        this.log(`🔍 WebSocket URL: ${this.ws.url}`);
                        this.log(`🔍 WebSocket protocol: ${this.ws.protocol}`);
                        this.updateStatus('✅ WebSocket pripojený', 'connected');
                        this.isConnected = true;

                        // Enable chat functions
                        this.elements.messageInput.disabled = false;
                        this.elements.sendBtn.disabled = false;
                        this.elements.startVoiceBtn.disabled = false;

                        this.addSystemMessage('✅ WebSocket pripojený! Používam real-time voice chat: WebSocket + Deepgram STT + OpenAI + Piper TTS.');

                        // Start ping/pong keepalive
                        this.startPingPong();
                    };

                    this.ws.onmessage = (event) => {
                        this.log(`📨 WebSocket správa prijatá (${typeof event.data})`);

                        if (typeof event.data === 'string') {
                            // Handle JSON messages
                            this.log(`📨 JSON správa: ${event.data.substring(0, 200)}${event.data.length > 200 ? '...' : ''}`);
                            try {
                                const data = JSON.parse(event.data);
                                this.handleMessage(data);
                            } catch (error) {
                                this.log(`❌ Chyba pri parsovaní správy: ${error.message}`);
                            }
                        } else {
                            // Handle binary audio data
                            this.log(`📨 Binary data: ${event.data.byteLength} bajtov`);
                            this.handleAudioData(event.data);
                        }
                    };

                    this.ws.onclose = (event) => {
                        this.log(`🔌 WebSocket zatvorený: ${event.code} - ${event.reason}`);
                        this.log(`🔍 Close event details:`);
                        this.log(`   - Code: ${event.code} (${this.getCloseCodeDescription(event.code)})`);
                        this.log(`   - Reason: "${event.reason}"`);
                        this.log(`   - Was clean: ${event.wasClean}`);
                        this.log(`   - Timestamp: ${new Date().toISOString()}`);

                        this.updateStatus('❌ WebSocket zatvorený', 'error');
                        this.isConnected = false;

                        // Stop ping/pong
                        this.stopPingPong();

                        // Disable chat functions
                        this.elements.messageInput.disabled = true;
                        this.elements.sendBtn.disabled = true;
                        this.elements.startVoiceBtn.disabled = true;
                        this.elements.stopVoiceBtn.disabled = true;

                        // Try to reconnect after 3 seconds (only for certain codes)
                        if (event.code !== 1000 && event.code !== 1001) {
                            setTimeout(() => {
                                if (!this.isConnected) {
                                    this.log('🔄 Pokúšam sa o opätovné pripojenie...');
                                    this.connectWebSocket();
                                }
                            }, 3000);
                        }
                    };

                    this.ws.onerror = (error) => {
                        this.log(`❌ WebSocket chyba: ${error}`);
                        this.updateStatus('❌ WebSocket chyba', 'error');
                    };

                } catch (error) {
                    this.log(`❌ Chyba pri vytváraní WebSocket: ${error.message}`);
                    this.updateStatus('❌ WebSocket chyba', 'error');
                }
            }

            startPingPong() {
                // Send ping every 25 seconds (server pings every 30s)
                this.pingInterval = setInterval(() => {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'ping',
                            timestamp: new Date().toISOString()
                        }));
                        this.log('🏓 Ping odoslaný');
                    }
                }, 25000);
            }

            stopPingPong() {
                if (this.pingInterval) {
                    clearInterval(this.pingInterval);
                    this.pingInterval = null;
                    this.log('🏓 Ping/pong zastavený');
                }
            }

            getCloseCodeDescription(code) {
                const codes = {
                    1000: 'Normal Closure',
                    1001: 'Going Away',
                    1002: 'Protocol Error',
                    1003: 'Unsupported Data',
                    1005: 'No Status Received',
                    1006: 'Abnormal Closure',
                    1007: 'Invalid frame payload data',
                    1008: 'Policy Violation',
                    1009: 'Message Too Big',
                    1010: 'Mandatory Extension',
                    1011: 'Internal Server Error',
                    1015: 'TLS Handshake'
                };
                return codes[code] || 'Unknown';
            }

            async handleMessage(data) {
                this.log(`📨 Správa: ${data.type}`);

                switch (data.type) {
                    case 'connection':
                        this.sessionId = data.sessionId;
                        this.elements.sessionInfo.textContent = data.sessionId.split('_')[1];
                        this.addSystemMessage(`🎉 ${data.message}`);
                        if (data.features) {
                            this.log(`🎯 Dostupné funkcie: ${data.features.join(', ')}`);
                        }
                        break;

                    case 'transcription':
                        this.log(`📝 Transkripcia: "${data.transcript}"`);
                        this.addMessage('user', data.transcript);
                        this.elements.voiceStatus.textContent = '🤖 Generujem odpoveď...';
                        break;

                    case 'ai_response':
                        this.log(`🤖 AI odpoveď: "${data.message}"`);
                        this.addMessage('assistant', data.message);
                        this.elements.voiceStatus.textContent = '🔊 Generujem TTS...';
                        break;

                    case 'voice_ended':
                        this.log(`🔇 Voice session ukončená`);
                        this.elements.voiceStatus.textContent = '🎤 Pripravený na rozpoznávanie hlasu...';
                        break;

                    case 'pong':
                        this.log(`🏓 Pong prijatý`);
                        break;

                    case 'ai_response':
                        // Fallback pre Web Speech API TTS ak Piper nie je dostupný
                        this.addMessage('assistant', data.message);
                        this.elements.voiceStatus.textContent = '🔊 Čítam odpoveď...';
                        await this.playOptimizedWebSpeechTTS(data.message);
                        this.resetDetectionStateForNewConversation();
                        break;

                    case 'error':
                        this.log(`❌ Chyba: ${data.message}`);
                        this.addSystemMessage(`❌ ${data.message}`);
                        this.elements.voiceStatus.textContent = '❌ Chyba pri spracovaní';
                        // Reset detection state after error to allow new conversation
                        this.resetDetectionStateForNewConversation();
                        break;

                    default:
                        this.log(`❓ Neznámy typ správy: ${data.type}`);
                }
            }

            handleAudioData(audioBuffer) {
                try {
                    this.log(`🔊 Prijatý TTS audio: ${audioBuffer.byteLength} bajtov`);

                    // Convert ArrayBuffer to Blob
                    const audioBlob = new Blob([audioBuffer], { type: 'audio/wav' });
                    const audioUrl = URL.createObjectURL(audioBlob);

                    // Create and play audio
                    const audio = new Audio(audioUrl);

                    audio.onloadeddata = () => {
                        this.log(`🔊 TTS audio načítané, dĺžka: ${audio.duration.toFixed(2)}s`);
                        this.elements.voiceStatus.textContent = '🔊 Prehrávam TTS...';
                    };

                    audio.onended = () => {
                        this.log(`✅ TTS prehrávanie dokončené`);
                        URL.revokeObjectURL(audioUrl);
                        this.isPlayingResponse = false;
                        this.resetDetectionStateForNewConversation();
                    };

                    audio.onerror = (error) => {
                        this.log(`❌ TTS audio chyba: ${error}`);
                        URL.revokeObjectURL(audioUrl);
                        this.isPlayingResponse = false;
                        this.resetDetectionStateForNewConversation();
                    };

                    this.isPlayingResponse = true;
                    audio.play().catch(error => {
                        this.log(`❌ Chyba pri prehrávaní TTS: ${error.message}`);
                        this.isPlayingResponse = false;
                        this.resetDetectionStateForNewConversation();
                    });

                } catch (error) {
                    this.log(`❌ Chyba pri spracovaní TTS audio: ${error.message}`);
                    this.isPlayingResponse = false;
                    this.resetDetectionStateForNewConversation();
                }
            }

            async sendMessage() {
                const message = this.elements.messageInput.value.trim();
                if (!message) return;

                if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                    this.addSystemMessage('❌ WebSocket nie je pripojený');
                    return;
                }

                this.addMessage('user', message);
                this.elements.messageInput.value = '';
                this.elements.sendBtn.disabled = true;

                try {
                    this.log(`📤 Posielam správu cez WebSocket: "${message}"`);
                    this.updateStatus('Spracovávam správu...', 'processing');

                    // Send message via WebSocket
                    const messageData = {
                        type: 'text_chat',
                        message: message,
                        sessionId: this.sessionId,
                        timestamp: new Date().toISOString()
                    };

                    this.ws.send(JSON.stringify(messageData));
                    this.log(`✅ Správa odoslaná cez WebSocket`);

                } catch (error) {
                    this.log(`❌ Chyba odosielania: ${error.message}`);
                    this.addSystemMessage('❌ Chyba pri odosielaní správy');
                    this.updateStatus('❌ Chyba spracovania', 'error');
                } finally {
                    this.elements.sendBtn.disabled = false;
                }
            }

            // WebSocket workflow - all communication via WebSocket

            // TTS is now handled via WebSocket from Oracle backend
            // Audio data comes as binary WebSocket messages and is played via handleAudioData()





            async playOptimizedWebSpeechTTS(text) {
                if (!window.speechSynthesis) {
                    this.log('❌ Web Speech API nie je podporované');
                    this.isPlayingResponse = false;
                    return;
                }

                try {
                    // Wait for voices to be fully loaded for best quality
                    await this.ensureVoicesLoaded();

                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.lang = 'sk-SK';
                    utterance.rate = 0.95; // Optimálne tempo pre slovenčinu
                    utterance.pitch = 1.0;
                    utterance.volume = 0.9;

                    // Find the best Slovak voice available
                    const bestVoice = this.findBestSlovakVoice();
                    if (bestVoice) {
                        utterance.voice = bestVoice;
                        this.log(`🎵 Používam najlepší slovenský hlas: ${bestVoice.name} (${bestVoice.lang})`);
                    } else {
                        this.log(`🎵 Používam predvolený hlas pre slovenčinu`);
                    }

                    return new Promise((resolve) => {
                        utterance.onstart = () => {
                            this.log(`🔊 Kvalitný TTS spustený`);
                        };

                        utterance.onend = () => {
                            this.log(`✅ TTS dokončené s vysokou kvalitou`);
                            this.isPlayingResponse = false;
                            resolve();
                        };

                        utterance.onerror = (event) => {
                            this.log(`❌ TTS chyba: ${event.error}`);
                            this.isPlayingResponse = false;
                            resolve();
                        };

                        // Clear any pending speech and start new one
                        speechSynthesis.cancel();
                        speechSynthesis.speak(utterance);
                    });

                } catch (error) {
                    this.log(`❌ TTS chyba: ${error.message}`);
                    this.isPlayingResponse = false;
                }
            }

            async ensureVoicesLoaded() {
                return new Promise((resolve) => {
                    const voices = speechSynthesis.getVoices();
                    if (voices.length > 0) {
                        resolve();
                    } else {
                        speechSynthesis.onvoiceschanged = () => {
                            resolve();
                        };
                        // Fallback timeout
                        setTimeout(resolve, 1000);
                    }
                });
            }

            findBestSlovakVoice() {
                const voices = speechSynthesis.getVoices();

                // Priority order for Slovak voices
                const voicePriorities = [
                    // Native Slovak voices
                    (voice) => voice.lang === 'sk-SK' && voice.localService,
                    (voice) => voice.lang === 'sk-SK',
                    (voice) => voice.lang.startsWith('sk'),
                    // Named Slovak voices
                    (voice) => voice.name.toLowerCase().includes('zuzana'),
                    (voice) => voice.name.toLowerCase().includes('laura'),
                    (voice) => voice.name.toLowerCase().includes('slovak'),
                    // Czech as fallback (similar language)
                    (voice) => voice.lang === 'cs-CZ',
                    (voice) => voice.lang.startsWith('cs'),
                    // Local voices (better quality)
                    (voice) => voice.localService && voice.lang.includes('en'),
                ];

                for (const priority of voicePriorities) {
                    const voice = voices.find(priority);
                    if (voice) {
                        return voice;
                    }
                }

                return null;
            }

            addMessage(role, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;

                messageDiv.appendChild(contentDiv);
                this.elements.chatMessages.appendChild(messageDiv);

                this.scrollToBottom();
            }

            addSystemMessage(content) {
                this.addMessage('system', content);
            }

            log(message) {
                const time = new Date().toLocaleTimeString();
                this.elements.log.innerHTML += `[${time}] ${message}\n`;
                this.elements.log.scrollTop = this.elements.log.scrollHeight;
                console.log(message);
            }

            updateStatus(message, className) {
                this.elements.status.textContent = message;
                this.elements.status.className = `status-indicator status-${className}`;
            }

            scrollToBottom() {
                this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
            }

            // Voice Activity Detection functions (from test_silence_detection)
            async startVoiceChat() {
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    this.addSystemMessage('❌ Váš prehliadač nepodporuje mikrofón. Skúste Chrome, Firefox alebo Safari.');
                    return;
                }

                try {
                    this.log('🚀 Spúšťam voice chat s Voice Activity Detection...');
                    this.updateStatus('Žiadam o prístup k mikrofónu...', 'processing');

                    // Request microphone access
                    const stream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true,
                            sampleRate: 16000
                        }
                    });

                    this.log('✅ Mikrofón prístup udelený');

                    // Initialize Web Audio API
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.analyser = this.audioContext.createAnalyser();
                    this.microphone = this.audioContext.createMediaStreamSource(stream);

                    this.analyser.smoothingTimeConstant = 0.3;
                    this.analyser.fftSize = 512;
                    this.microphone.connect(this.analyser);

                    this.log('✅ Web Audio API inicializované');

                    // Setup MediaRecorder for audio capture with Deepgram-compatible format
                    // Preferujte Opus - Deepgram ho podporuje najlepšie
                    let mimeType = 'audio/webm;codecs=opus';
                    if (!MediaRecorder.isTypeSupported(mimeType)) {
                        // fallback na obyčajný WebM - NIKDY nepoužívame PCM!
                        mimeType = 'audio/webm';
                        if (!MediaRecorder.isTypeSupported(mimeType)) {
                            throw new Error('Prehliadač nepodporuje WebM formát potrebný pre Deepgram');
                        }
                    }
                    this.log(`🎤 Používam ${mimeType} pre Deepgram`);

                    this.mediaRecorder = new MediaRecorder(stream, {
                        mimeType: mimeType
                    });

                    this.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            this.audioChunks.push(event.data);
                        }
                    };

                    this.mediaRecorder.onstop = () => {
                        this.log(`🎵 Nahrávka dokončená (${this.audioChunks.length} chunks)`);
                    };

                    this.mediaRecorder.start(50); // OPTIMALIZÁCIA: 50ms chunks pre rýchlejšiu detekciu
                    this.log('✅ MediaRecorder spustený');

                    // Start voice activity detection
                    this.isRunning = true;
                    this.waitingForFirstToken = true;
                    this.elements.startVoiceBtn.disabled = true;
                    this.elements.stopVoiceBtn.disabled = false;
                    this.elements.voiceControls.style.display = 'block';

                    this.log('✅ Mikrofón pripojený, začínam Voice Activity Detection...');
                    this.updateStatus('Počúvam... (hovorte po slovensky)', 'connected');
                    this.elements.voiceStatus.textContent = '🎤 Počúvam... hovorte po slovensky';

                    this.monitorAudio();

                } catch (error) {
                    this.log(`❌ Chyba pri spustení voice chat: ${error.message}`);
                    this.addSystemMessage(`❌ Nepodarilo sa spustiť voice chat: ${error.message}`);
                    this.updateStatus('❌ Chyba voice chat', 'error');

                    if (error.name === 'NotAllowedError') {
                        this.addSystemMessage('💡 Tip: Kliknite na ikonu zámku v adresnom riadku a povoľte mikrofón.');
                    }
                }
            }

            stopVoiceChat() {
                this.log('⏹️ Zastavujem voice chat...');

                this.isRunning = false;

                if (this.silenceTimer) {
                    clearTimeout(this.silenceTimer);
                    this.silenceTimer = null;
                }

                if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
                    try {
                        this.mediaRecorder.stop();
                    } catch (error) {
                        this.log(`❌ Chyba pri zastavení MediaRecorder: ${error.message}`);
                    }
                    this.mediaRecorder = null;
                }

                if (this.audioContext) {
                    try {
                        this.audioContext.close();
                    } catch (error) {
                        this.log(`❌ Chyba pri zatvorení AudioContext: ${error.message}`);
                    }
                    this.audioContext = null;
                }

                this.audioChunks = [];

                this.elements.startVoiceBtn.disabled = false;
                this.elements.stopVoiceBtn.disabled = true;
                this.elements.voiceControls.style.display = 'none';
                this.elements.levelBar.style.width = '0%';

                this.log('✅ Voice chat zastavený');
                this.updateStatus('Voice chat zastavený', 'disconnected');
            }

            // Voice Activity Detection (exact logic from test_silence_detection)
            detectHumanVoice(frequencyData, sampleRate) {
                const nyquist = sampleRate / 2;
                const binWidth = nyquist / frequencyData.length;

                const speechBands = {
                    fundamental: { min: 85, max: 300 },
                    formant1: { min: 300, max: 1000 },
                    formant2: { min: 800, max: 2500 },
                    highFreq: { min: 2500, max: 4000 }
                };

                let fundamentalEnergy = 0;
                let formant1Energy = 0;
                let formant2Energy = 0;
                let totalEnergy = 0;

                for (let i = 0; i < frequencyData.length; i++) {
                    const freq = i * binWidth;
                    const energy = frequencyData[i];
                    totalEnergy += energy;

                    if (freq >= speechBands.fundamental.min && freq <= speechBands.fundamental.max) {
                        fundamentalEnergy += energy;
                    } else if (freq >= speechBands.formant1.min && freq <= speechBands.formant1.max) {
                        formant1Energy += energy;
                    } else if (freq >= speechBands.formant2.min && freq <= speechBands.formant2.max) {
                        formant2Energy += energy;
                    }
                }

                if (totalEnergy === 0) return false;

                const fundamentalRatio = fundamentalEnergy / totalEnergy;
                const formant1Ratio = formant1Energy / totalEnergy;
                const formant2Ratio = formant2Energy / totalEnergy;
                const speechEnergyRatio = (fundamentalEnergy + formant1Energy + formant2Energy) / totalEnergy;

                const hasStrongFormants = formant1Ratio > 0.15 || formant2Ratio > 0.1;
                const hasSpeechSpectrum = speechEnergyRatio > 0.4;
                const hasReasonableFundamental = fundamentalRatio > 0.05 && fundamentalRatio < 0.5;

                const voiceScore = (hasStrongFormants ? 1 : 0) + (hasSpeechSpectrum ? 1 : 0) + (hasReasonableFundamental ? 1 : 0);
                return voiceScore >= 2;
            }

            monitorAudio() {
                if (!this.isRunning || !this.analyser || this.isPlayingResponse) {
                    if (this.isRunning && !this.isPlayingResponse) {
                        requestAnimationFrame(() => this.monitorAudio());
                    }
                    return;
                }

                const bufferLength = this.analyser.frequencyBinCount;
                const dataArray = new Uint8Array(bufferLength);

                this.analyser.getByteFrequencyData(dataArray);

                const rms = Math.sqrt(dataArray.reduce((sum, value) => sum + value * value, 0) / bufferLength);
                const isHumanVoice = this.detectHumanVoice(dataArray, this.analyser.context.sampleRate);

                const threshold = 50;
                const isValidSpeech = rms > threshold && isHumanVoice;

                // Update level meter
                const percentage = Math.min((rms / 50) * 100, 100);
                this.elements.levelBar.style.width = percentage + '%';

                const currentTime = Date.now();

                if (isValidSpeech && !this.isPlayingResponse) {
                    this.consecutiveSpeechCount++;
                    this.consecutiveSilenceCount = 0;

                    if (this.consecutiveSpeechCount === 1) {
                        this.speechStartTime = currentTime;
                        this.lastTokenTime = currentTime;
                        if (this.waitingForFirstToken) {
                            this.tokenCount = 0;
                        }
                        this.log(`🔍 Možná reč detekovaná (RMS: ${rms.toFixed(1)}, threshold: ${threshold})`);
                    }

                    // Token detection
                    const timeSinceLastToken = currentTime - this.lastTokenTime;

                    if (timeSinceLastToken > 50) {
                        this.tokenCount++;
                        this.lastTokenTime = currentTime;

                        if (this.waitingForFirstToken && this.tokenCount === 1) {
                            this.lastSpeechTime = currentTime;
                            this.waitingForFirstToken = false;
                            this.log(`🚀 PRVÝ TOKEN novej konverzácie - spúšťam 1200ms timer! (RMS: ${rms.toFixed(1)})`);
                            this.elements.voiceStatus.textContent = '🗣️ Rozprávate... (1200ms timer spustený)';
                        } else {
                            this.lastSpeechTime = currentTime;
                            this.log(`📊 Token ${this.tokenCount} detekovaný (RMS: ${rms.toFixed(1)}) - audio chunks: ${this.audioChunks.length}`);
                        }
                    }

                    if (this.consecutiveSpeechCount >= 8 && !this.isSpeaking) {
                        this.isSpeaking = true;
                        this.currentTranscript = '';
                        this.consecutiveSilenceCount = 0;
                        this.log('🎤 Konzistentná reč detekovaná - začínam počúvať...');
                        this.elements.voiceStatus.textContent = '🗣️ Rozprávate... (urobte pauzu 1200ms)';
                    }

                    if (this.isSpeaking) {
                        this.consecutiveSilenceCount = 0;
                    }

                    if (this.silenceTimer) {
                        clearTimeout(this.silenceTimer);
                        this.silenceTimer = null;
                    }
                } else {
                    this.consecutiveSilenceCount++;
                    this.consecutiveSpeechCount = 0;

                    if (this.consecutiveSilenceCount >= 5) {
                        if (this.consecutiveSpeechCount > 0 && this.consecutiveSpeechCount < 3) {
                            const timeSinceStart = currentTime - this.speechStartTime;
                            this.log(`🔇 Krátky šum ignorovaný (${this.consecutiveSpeechCount} detekcií, ${this.tokenCount} tokenov, ${timeSinceStart}ms)`);

                            this.consecutiveSpeechCount = 0;
                            this.tokenCount = 0;
                            this.speechStartTime = 0;
                            this.lastTokenTime = 0;
                        }
                    }

                    // OPTIMALIZÁCIA: 1200ms timeout pre lepšiu detekciu konca reči
                    const timeSinceLastToken = currentTime - this.lastTokenTime;
                    const hasTokens = this.tokenCount > 0;
                    const tokenTimeout = 1200;

                    if (hasTokens && timeSinceLastToken > tokenTimeout && !this.isPlayingResponse) {
                        this.log(`⏰ ${tokenTimeout}ms timeout od posledného tokenu - spracovávam audio`);
                        this.log(`📊 Tokeny: ${this.tokenCount}, Čas od posledného tokenu: ${timeSinceLastToken}ms, Timeout: ${tokenTimeout}ms`);

                        const speechDuration = currentTime - this.speechStartTime;
                        this.log(`🔍 Debug: audioChunks=${this.audioChunks.length}, speechDuration=${speechDuration}ms`);
                        this.log(`📡 Spracovávam audio...`);
                        this.processStreamAudio();
                        return;
                    }
                }

                if (this.isRunning) {
                    requestAnimationFrame(() => this.monitorAudio());
                }
            }

            async processStreamAudio() {
                try {
                    if (this.audioChunks.length === 0) {
                        this.log('⚠️ Žiadne audio chunks na spracovanie');
                        this.elements.voiceStatus.textContent = '🎤 Počúvam... hovorte po slovensky';
                        return;
                    }

                    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                        this.log('❌ WebSocket nie je pripojený');
                        this.elements.voiceStatus.textContent = '❌ WebSocket nie je pripojený';
                        return;
                    }

                    this.log(`🎵 Spracovávam audio (${this.audioChunks.length} chunks) - používam WebSocket streaming...`);
                    this.elements.voiceStatus.textContent = '📡 Posielam audio cez WebSocket...';

                    // Create audio blob from chunks
                    const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
                    this.log(`🎵 Audio blob vytvorený: ${audioBlob.size} bajtov, type: ${audioBlob.type}`);

                    // Convert blob to ArrayBuffer for WebSocket binary transmission
                    const audioBuffer = await audioBlob.arrayBuffer();
                    this.log(`🎵 Audio buffer pripravený: ${audioBuffer.byteLength} bajtov`);

                    // Send binary audio data via WebSocket
                    this.ws.send(audioBuffer);
                    this.log(`📡 Audio odoslaný cez WebSocket`);

                    this.elements.voiceStatus.textContent = '🔄 Spracovávam audio...';

                    // Clear audio chunks for next recording
                    this.audioChunks = [];

                    // Set flag to prevent new detection until response is complete
                    this.isPlayingResponse = true;
                    this.log('🔒 Nastavujem isPlayingResponse=true - čakám na transkripciu, AI odpoveď a TTS');

                } catch (error) {
                    this.log(`❌ Chyba pri spracovaní audio: ${error.message}`);
                    this.elements.voiceStatus.textContent = '❌ Chyba pri spracovaní audio';
                    this.addSystemMessage(`❌ Chyba pri spracovaní hlasu: ${error.message}`);

                    // Reset state
                    this.audioChunks = [];
                    this.resetDetectionStateForNewConversation();
                }
            }

            resetDetectionStateForNewConversation() {
                this.log('🔄 Resetujem detection state pre novú konverzáciu...');

                this.isPlayingResponse = false;
                this.silenceTimer = null;
                this.audioChunks = [];
                this.consecutiveSpeechCount = 0;
                this.consecutiveSilenceCount = 0;
                this.tokenCount = 0;
                this.speechStartTime = 0;
                this.lastTokenTime = 0;
                this.isSpeaking = false;
                this.waitingForFirstToken = true;

                this.elements.voiceStatus.textContent = '🎤 Počúvam... (čakám na prvý token novej otázky)';
                this.log('🔄 Nová konverzácia - čakám na prvý token, potom spustím 900ms timer...');

                if (this.isRunning && this.analyser) {
                    this.log('🔄 Reštartujem audio monitoring pre novú konverzáciu...');
                    this.monitorAudio();
                }
            }
        }

        // Initialize application
        document.addEventListener('DOMContentLoaded', () => {
            window.cloudflareChat = new CloudflareOracleChat();
        });
    </script>
</body>
</html>
